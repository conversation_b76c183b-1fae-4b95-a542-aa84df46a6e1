<?php

namespace App\SwaggerDocs\Api;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'Organization',
    title: 'Organization',
    required: ['id', 'name'],
    properties: [
        new OA\Property(property: 'id', type: 'integer', example: 1),
        new OA\Property(property: 'name', type: 'string', example: 'Организация "Пример"'),
        new OA\Property(property: 'building_id', type: 'integer', example: 1),
        new OA\Property(property: 'description', type: 'string', example: 'Описание организации'),
        new OA\Property(property: 'building', ref: '#/components/schemas/Building'),
        new OA\Property(
            property: 'phones',
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'number', type: 'string', example: '+7 (999) 123-45-67'),
                ],
                type: 'object'
            )
        ),
        new OA\Property(
            property: 'activities',
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'name', type: 'string', example: 'Медицинские услуги'),
                ],
                type: 'object'
            )
        ),
    ],
    type: 'object'
)]
class OrganizationDoc
{
    #[OA\Get(
        path: '/api/organizations/{id}',
        operationId: 'getOrganizationById',
        description: 'Получить организацию по ID',
        summary: 'Показать организацию',
        security: [['bearerAuth' => []]],
        tags: ['Organizations'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'ID организации',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'integer', example: 1)
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Успешный ответ',
                content: new OA\JsonContent(ref: '#/components/schemas/Organization')
            ),
            new OA\Response(
                response: 404,
                description: 'Организация не найдена',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'message', type: 'string', example: 'Организация не найдена')
                    ]
                )
            )
        ]
    )]
    public function show()
    {
        // Документация для метода show OrganizationController
    }

    #[OA\Get(
        path: '/api/organizations/building/{buildingId}',
        operationId: 'getOrganizationsByBuilding',
        description: 'Получить организации в здании',
        summary: 'Организации по зданию',
        security: [['bearerAuth' => []]],
        tags: ['Organizations'],
        parameters: [
            new OA\Parameter(
                name: 'buildingId',
                description: 'ID здания',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'integer', example: 1)
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Успешный ответ',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/Organization')
                )
            )
        ]
    )]
    public function getByBuilding()
    {
        // Документация для метода getByBuilding OrganizationController
    }

    #[OA\Get(
        path: '/api/organizations/activity/{activityId}',
        operationId: 'getOrganizationsByActivity',
        description: 'Получить организации по виду деятельности',
        summary: 'Организации по деятельности',
        security: [['bearerAuth' => []]],
        tags: ['Organizations'],
        parameters: [
            new OA\Parameter(
                name: 'activityId',
                description: 'ID вида деятельности',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'integer', example: 1)
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Успешный ответ',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/Organization')
                )
            )
        ]
    )]
    public function getByActivity()
    {
        // Документация для метода getByActivity OrganizationController
    }

    #[OA\Post(
        path: '/api/organizations/search/activity/tree',
        operationId: 'searchOrganizationsByActivityTree',
        description: 'Поиск организаций по дереву видов деятельности (включая дочерние)',
        summary: 'Поиск по дереву деятельности',
        security: [['bearerAuth' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                required: ['activity_id'],
                properties: [
                    new OA\Property(property: 'activity_id', description: 'ID родительского вида деятельности', type: 'integer', example: 1)
                ]
            )
        ),
        tags: ['Organizations'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Успешный ответ',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/Organization')
                )
            ),
            new OA\Response(
                response: 422,
                description: 'Ошибка валидации',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'message', type: 'string', example: 'The given data was invalid.'),
                        new OA\Property(
                            property: 'errors',
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: 'activity_id',
                                    type: 'array',
                                    items: new OA\Items(type: 'string', example: 'The activity id field is required.')
                                )
                            ]
                        )
                    ]
                )
            )
        ]
    )]
    public function searchByActivityTree()
    {
        // Документация для метода searchByActivityTree OrganizationController
    }

    #[OA\Get(
        path: '/api/organizations/search/name',
        operationId: 'searchOrganizationsByName',
        description: 'Поиск организаций по названию',
        summary: 'Поиск по названию',
        security: [['bearerAuth' => []]],
        tags: ['Organizations'],
        parameters: [
            new OA\Parameter(
                name: 'name',
                description: 'Часть названия организации',
                in: 'query',
                required: true,
                schema: new OA\Schema(type: 'string', example: 'медицина')
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Успешный ответ',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/Organization')
                )
            ),
            new OA\Response(
                response: 422,
                description: 'Ошибка валидации',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'message', type: 'string', example: 'The given data was invalid.'),
                        new OA\Property(
                            property: 'errors',
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: 'name',
                                    type: 'array',
                                    items: new OA\Items(type: 'string', example: 'The name field is required.')
                                )
                            ]
                        )
                    ]
                )
            )
        ]
    )]
    public function searchByName()
    {
        // Документация для метода searchByName OrganizationController
    }

    #[OA\Post(
        path: '/api/organizations/search/geo/radius',
        operationId: 'getOrganizationsByGeoRadius',
        description: 'Поиск организаций в радиусе от точки',
        summary: 'Поиск по радиусу',
        security: [['bearerAuth' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                required: ['latitude', 'longitude', 'radius'],
                properties: [
                    new OA\Property(property: 'latitude', description: 'Широта центра поиска', type: 'number', format: 'float', example: 55.7558),
                    new OA\Property(property: 'longitude', description: 'Долгота центра поиска', type: 'number', format: 'float', example: 37.6176),
                    new OA\Property(property: 'radius', description: 'Радиус поиска в километрах', type: 'number', format: 'float', example: 5.0)
                ]
            )
        ),
        tags: ['Organizations'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Успешный ответ',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/Organization')
                )
            ),
            new OA\Response(
                response: 422,
                description: 'Ошибка валидации',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'message', type: 'string', example: 'The given data was invalid.'),
                        new OA\Property(
                            property: 'errors',
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: 'latitude',
                                    type: 'array',
                                    items: new OA\Items(type: 'string', example: 'The latitude field is required.')
                                ),
                                new OA\Property(
                                    property: 'longitude',
                                    type: 'array',
                                    items: new OA\Items(type: 'string', example: 'The longitude field is required.')
                                ),
                                new OA\Property(
                                    property: 'radius',
                                    type: 'array',
                                    items: new OA\Items(type: 'string', example: 'The radius field is required.')
                                )
                            ]
                        )
                    ]
                )
            )
        ]
    )]
    public function getByGeoRadius()
    {
        // Документация для метода getByGeoRadius OrganizationController
    }

    #[OA\Post(
        path: '/api/organizations/search/geo/rectangle',
        operationId: 'getOrganizationsByGeoRectangle',
        description: 'Поиск организаций в прямоугольной области',
        summary: 'Поиск по прямоугольнику',
        security: [['bearerAuth' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                required: ['north_latitude', 'south_latitude', 'east_longitude', 'west_longitude'],
                properties: [
                    new OA\Property(property: 'north_latitude', description: 'Северная широта', type: 'number', format: 'float', example: 55.8),
                    new OA\Property(property: 'south_latitude', description: 'Южная широта', type: 'number', format: 'float', example: 55.7),
                    new OA\Property(property: 'east_longitude', description: 'Восточная долгота', type: 'number', format: 'float', example: 37.7),
                    new OA\Property(property: 'west_longitude', description: 'Западная долгота', type: 'number', format: 'float', example: 37.5)
                ]
            )
        ),
        tags: ['Organizations'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Успешный ответ',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/Organization')
                )
            ),
            new OA\Response(
                response: 422,
                description: 'Ошибка валидации',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'message', type: 'string', example: 'The given data was invalid.'),
                        new OA\Property(
                            property: 'errors',
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: 'north_latitude',
                                    type: 'array',
                                    items: new OA\Items(type: 'string', example: 'The north latitude field is required.')
                                ),
                                new OA\Property(
                                    property: 'south_latitude',
                                    type: 'array',
                                    items: new OA\Items(type: 'string', example: 'The south latitude field is required.')
                                ),
                                new OA\Property(
                                    property: 'east_longitude',
                                    type: 'array',
                                    items: new OA\Items(type: 'string', example: 'The east longitude field is required.')
                                ),
                                new OA\Property(
                                    property: 'west_longitude',
                                    type: 'array',
                                    items: new OA\Items(type: 'string', example: 'The west longitude field is required.')
                                )
                            ]
                        )
                    ]
                )
            )
        ]
    )]
    public function getByGeoRectangle()
    {
        // Документация для метода getByGeoRectangle OrganizationController
    }
}
