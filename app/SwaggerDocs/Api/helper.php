<?php

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

if (! function_exists('blogProperties')) {
    function blogProperties(): array
    {
        return [
            new OA\Property(property: 'id', type: 'string'),
            new OA\Property(property: 'title', type: 'string'),
            new OA\Property(property: 'description', type: 'string'),
            new OA\Property(property: 'slug', type: 'string'),
            new OA\Property(property: 'status', type: 'string'),
            new OA\Property(property: 'createdAt', type: 'string'),
            new OA\Property(property: 'commentsCount', type: 'int'),
            new OA\Property(property: 'favoritesCount', type: 'int'),
            new OA\Property(property: 'subject', type: 'string'),
            new OA\Property(property: 'hasInFavorites', type: 'boolean', nullable: true),
            new OA\Property(property: 'publicationCount', type: 'int', nullable: true),
            new OA\Property(property: 'user', type: 'object', nullable: true, properties: [
                new OA\Property(property: 'id', type: 'string'),
                new OA\Property(property: 'firstname', type: 'string'),
                new OA\Property(property: 'lastname', type: 'string'),
                new OA\Property(property: 'avatarUrl', type: 'string', nullable: true),
            ]),
            new OA\Property(property: 'project', type: 'object', nullable: true, properties: [
                new OA\Property(property: 'id', type: 'string'),
                new OA\Property(property: 'title', type: 'string'),
                new OA\Property(property: 'slug', type: 'string'),
                new OA\Property(property: 'author', type: 'object', properties: [
                    new OA\Property(property: 'id', type: 'string'),
                ]),
            ]),
        ];
    }
}

if (! function_exists('loginPropertyDoc')) {
    function loginPropertyDoc(): OA\Property
    {
        return new OA\Property(property: 'data', type: 'object', properties: [
            new OA\Property(property: 'success', type: 'boolean'),
            new OA\Property(property: 'tokenType', type: 'string'),
            new OA\Property(property: 'token', type: 'string'),
            new OA\Property(property: 'expiresAt', type: 'number'),
            new OA\Property(property: 'refreshToken', type: 'string'),
            new OA\Property(property: 'user', type: 'object', properties: [
                new OA\Property(property: 'id', type: 'number'),
                new OA\Property(property: 'email', type: 'string'),
                new OA\Property(property: 'filled', type: 'boolean'),
                new OA\Property(property: 'firstname', type: 'string'),
                new OA\Property(property: 'lastname', type: 'string'),
                new OA\Property(property: 'avatarUrl', type: 'string'),
            ]),
        ]);
    }
}

if (! function_exists('chatMessagePropertiesDoc')) {
    function chatMessagePropertiesDoc(): array
    {
        return [
            new OA\Property(property: 'id', type: 'number'),
            new OA\Property(property: 'from', type: 'number'),
            new OA\Property(property: 'to', type: 'number'),
            new OA\Property(property: 'read', type: 'number/boolean'),
            new OA\Property(property: 'text', type: 'string'),
            new OA\Property(property: 'created_at', type: 'string'),
            new OA\Property(property: 'type', type: 'string'),
        ];
    }
}

if (! function_exists('chatContactPropertiesDoc')) {
    function chatContactPropertiesDoc(): array
    {
        return [
            new OA\Property(property: 'id', type: 'string'),
            new OA\Property(property: 'firstname', type: 'string'),
            new OA\Property(property: 'lastname', type: 'string'),
            new OA\Property(property: 'isOnline', type: 'boolean'),
            new OA\Property(property: 'lastOnlineAt', type: 'string'),
            new OA\Property(property: 'unreadMessagesCount', type: 'number'),
            new OA\Property(property: 'avatarUrl', type: 'string'),
            new OA\Property(property: 'lastMessage', type: 'object',
                properties: chatMessagePropertiesDoc()
            ),
        ];
    }
}

if (! function_exists('unauthorizedResponseDoc')) {
    function unauthorizedResponseDoc(): OA\Response
    {
        return new OA\Response(response: Response::HTTP_UNAUTHORIZED, description: 'Unauthorized',
            content: new OA\JsonContent(properties: [
                new OA\Property(property: 'message', type: 'string', example: 'Unauthenticated.'),
            ])
        );
    }
}

if (! function_exists('paginationJsonContentDoc')) {
    function paginationJsonContentDoc(array $properties): OA\JsonContent
    {
        return new OA\JsonContent(properties: [
            new OA\Property(property: 'data', type: 'array', items: new OA\Items(
                properties: $properties
            )),
            new OA\Property(property: 'meta', type: 'object', properties: [
                new OA\Property(property: 'current_page', type: 'int'),
                new OA\Property(property: 'from', type: 'int'),
                new OA\Property(property: 'last_page', type: 'int'),
                new OA\Property(property: 'path', type: 'number'),
                new OA\Property(property: 'per_page', type: 'int'),
                new OA\Property(property: 'to', type: 'int'),
                new OA\Property(property: 'total', type: 'int'),
                new OA\Property(property: 'links', type: 'array', items: new OA\Items(
                    properties: [
                        new OA\Property(property: 'url', type: 'string'),
                        new OA\Property(property: 'label', type: 'string'),
                        new OA\Property(property: 'active', type: 'boolean'),
                    ]
                )),
            ]),
            new OA\Property(property: 'links', type: 'object', properties: [
                new OA\Property(property: 'first', type: 'string', nullable: true),
                new OA\Property(property: 'last', type: 'string', nullable: true),
                new OA\Property(property: 'prev', type: 'string', nullable: true),
                new OA\Property(property: 'next', type: 'string', nullable: true),
            ]),
        ]);
    }
}

if (! function_exists('badRequestResponseDoc')) {
    function badRequestResponseDoc(): OA\Response
    {
        return new OA\Response(response: Response::HTTP_BAD_REQUEST, description: 'Bad Request',
            content: new OA\JsonContent(properties: [
                new OA\Property(property: 'message', type: 'string'),
            ])
        );
    }
}

if (! function_exists('notFoundResponseDoc')) {
    function notFoundResponseDoc(): OA\Response
    {
        return new OA\Response(response: Response::HTTP_NOT_FOUND, description: 'Not Found',
            content: new OA\JsonContent(properties: [
                new OA\Property(property: 'message', type: 'string'),
            ])
        );
    }
}

if (! function_exists('unprocessableEntityResponseDoc')) {
    function unprocessableEntityResponseDoc(): OA\Response
    {
        return new OA\Response(response: Response::HTTP_UNPROCESSABLE_ENTITY, description: 'Unprocessable entity',
            content: new OA\JsonContent(properties: [
                new OA\Property(property: 'message', type: 'string'),
            ])
        );
    }
}

if (! function_exists('forbiddenResponseDoc')) {
    function forbiddenResponseDoc(): OA\Response
    {
        return new OA\Response(response: Response::HTTP_FORBIDDEN, description: 'Forbidden',
            content: new OA\JsonContent(properties: [
                new OA\Property(property: 'message', type: 'string'),
            ])
        );
    }
}

if (! function_exists('conflictReCaptchaResponseDoc')) {
    function conflictReCaptchaResponseDoc(): OA\Response
    {
        return new OA\Response(response: Response::HTTP_CONFLICT, description: 'Conflict - Google ReCaptcha',
            content: new OA\JsonContent(properties: [
                new OA\Property(property: 'message', type: 'string'),
            ])
        );
    }
}

if (! function_exists('blogBelongsToJsonContentDoc')) {
    function blogBelongsToJsonContentDoc(): OA\JsonContent
    {
        return new OA\JsonContent(properties: [
            new OA\Property(property: 'data', type: 'object', properties: [
                ...blogProperties(),
                new OA\Property(property: 'belongsTo', type: 'object', nullable: true, properties: [
                    new OA\Property(property: 'id', type: 'string'),
                    new OA\Property(property: 'authorId', type: 'string'),
                    new OA\Property(property: 'title', type: 'string'),
                    new OA\Property(property: 'publicationCount', type: 'int'),
                    new OA\Property(property: 'slug', type: 'string', nullable: true),
                    new OA\Property(property: 'avatarUrl', type: ['string'], nullable: true),
                ]),
            ]),
        ]);
    }
}

if (! function_exists('blogBelongsToListJsonContentDoc')) {
    function blogBelongsToListJsonContentDoc(OA\JsonContent $content): OA\JsonContent
    {
        return new OA\JsonContent(properties: [
            new OA\Property(property: 'data', type: 'object', properties: [
                new OA\Property(property: 'blogs', type: 'object', properties: $content->properties),
                new OA\Property(property: 'belongsTo', type: 'object', nullable: true, properties: [
                    new OA\Property(property: 'id', type: 'string'),
                    new OA\Property(property: 'authorId', type: 'string'),
                    new OA\Property(property: 'title', type: 'string'),
                    new OA\Property(property: 'publicationCount', type: 'int'),
                    new OA\Property(property: 'slug', type: 'string', nullable: true),
                    new OA\Property(property: 'avatarUrl', type: ['string'], nullable: true),
                ]),
            ]),
        ]);
    }
}

if (! function_exists('blogJsonContentDoc')) {
    function blogJsonContentDoc(): OA\JsonContent
    {
        return new OA\JsonContent(properties: [
            new OA\Property(property: 'data', type: 'object', properties: [
                ...blogProperties(),
            ]),
        ]);
    }
}

if (! function_exists('totalJsonContentDoc')) {
    function totalJsonContentDoc(): OA\JsonContent
    {
        return new OA\JsonContent(properties: [
            new OA\Property(property: 'total', type: 'number'),
        ]);
    }
}

if (! function_exists('projectCardPropertiesDoc')) {
    function projectCardPropertiesDoc(): array
    {
        return [
            new OA\Property(property: 'id', type: 'string'),
            new OA\Property(property: 'type', type: 'string'),
            new OA\Property(property: 'slug', type: 'string'),
            new OA\Property(property: 'title', type: 'string'),
            new OA\Property(property: 'description', type: 'string'),
            new OA\Property(property: 'createdAt', type: 'string'),
            new OA\Property(property: 'status', type: 'string|null'),
            new OA\Property(property: 'favoritesCount', type: 'number'),
            new OA\Property(property: 'commentsCount', type: 'number'),
        ];
    }
}

if (! function_exists('userCardPropertiesDoc')) {
    function userCardPropertiesDoc(): array
    {
        return [
            new OA\Property(property: 'id', type: 'string'),
            new OA\Property(property: 'firstname', type: 'string'),
            new OA\Property(property: 'lastname', type: 'string'),
            new OA\Property(property: 'avatarUrl', type: 'string'),
            new OA\Property(property: 'isOnline', type: 'boolean'),
            new OA\Property(property: 'lastOnlineAt', type: 'string'),
            new OA\Property(property: 'createdAt', type: 'string'),
            new OA\Property(property: 'desiredPosition', type: 'string'),
            new OA\Property(property: 'skills', type: 'array', items: new OA\Items(
                type: 'string'
            )),
        ];
    }
}

if (! function_exists('createProjectPropertiesDoc')) {
    function createProjectPropertiesDoc(): array
    {
        return [
            new OA\Property(property: 'requireForTeamTags', description: 'Project team tags or null', type: 'string', example: '["PHP Developer","Java Developer"]'),
            new OA\Property(property: 'projectTags', description: 'Project tags or null', type: 'string', example: '["php","Java"]'),
            new OA\Property(property: 'country', description: 'Country or null', type: 'string', example: '{"id":2,"title":"Palau"}'),
            new OA\Property(property: 'city', description: 'City or null', type: 'string', example: '{"id":230,"region":"impedit","aria":null,"regionId":21,"city":"Lake Gaston","title":"impedit, Lake Gaston"}'),
            new OA\Property(property: 'title', description: 'Title', type: 'string', minLength: 3, maxLength: 500, example: 'Andrey'),
            new OA\Property(property: 'about', description: 'About project', type: 'string'),
            new OA\Property(property: 'description', description: 'Description', type: 'string', maxLength: 200),
        ];
    }
}

if (! function_exists('projectResponsePropertiesDoc')) {
    function projectResponsePropertiesDoc(): array
    {
        return [
            new OA\Property(property: 'data', type: 'object',
                properties: [
                    new OA\Property(property: 'id', type: 'string'),
                    new OA\Property(property: 'success', type: 'boolean'),
                    new OA\Property(property: 'slug', type: 'string'),
                    new OA\Property(property: 'favoritesCount', type: 'number'),
                ]
            ),
        ];
    }
}

if (! function_exists('requireTeamTagsPropertiesDoc')) {
    function requireTeamTagsPropertiesDoc(): array
    {
        return [
            new OA\Property(property: 'id', type: 'string'),
            new OA\Property(property: 'title', type: 'string', minLength: 2),
            new OA\Property(property: 'status', type: 'string'),
            new OA\Property(property: 'isHidden', type: 'boolean'),
            new OA\Property(property: 'previous', type: 'string | null'),
        ];
    }
}

if (! function_exists('projectAuthorPropertiesDoc')) {
    function projectAuthorPropertiesDoc(): array
    {
        return [
            new OA\Property(property: 'id', type: 'string'),
            new OA\Property(property: 'firstname', type: 'string'),
            new OA\Property(property: 'lastname', type: 'string'),
            new OA\Property(property: 'avatarUrl', type: 'string'),
            new OA\Property(property: 'isOnline', type: 'boolean'),
            new OA\Property(property: 'lastOnlineAt', type: 'string'),
        ];
    }
}

if (! function_exists('roleInTeamPropertiesDoc')) {
    function roleInTeamPropertiesDoc(): array
    {
        return [
            new OA\Property(property: 'id', type: 'string'),
            new OA\Property(property: 'title', type: 'string'),
            new OA\Property(property: 'description', type: 'string'),
        ];
    }
}

if (! function_exists('projectDetailPropertiesDoc')) {
    function projectDetailPropertiesDoc(): array
    {
        return [
            new OA\Property(property: 'id', type: 'string'),
            new OA\Property(property: 'slug', type: 'string'),
            new OA\Property(property: 'title', type: 'string'),
            new OA\Property(property: 'about', type: 'string'),
            new OA\Property(property: 'status', type: 'string|null'),
            new OA\Property(property: 'projectTags', type: 'array', items: new OA\Items(properties: [
                new OA\Property(property: 'id', type: 'string'),
                new OA\Property(property: 'title', type: 'string'),
                new OA\Property(property: 'status', type: 'string'),
            ])),
            new OA\Property(property: 'description', type: 'string'),
            new OA\Property(property: 'createdAt', type: 'string'),
            new OA\Property(property: 'requireForTeamTags', type: 'array', items: new OA\Items(
                properties: requireTeamTagsPropertiesDoc())
            ),
            new OA\Property(property: 'author', type: 'object', properties: projectAuthorPropertiesDoc()
            ),
            new OA\Property(property: 'location', type: 'object', properties: [
                new OA\Property(property: 'city', type: 'string'),
                new OA\Property(property: 'region', type: 'string'),
                new OA\Property(property: 'country', type: 'string'),
            ]),
            new OA\Property(property: 'hasInBookmarks', type: 'boolean'),
            new OA\Property(property: 'hasInFavorites', type: 'boolean'),
            new OA\Property(property: 'favoritesCount', type: 'number'),
            new OA\Property(property: 'hasInComplaints', type: 'boolean'),
            new OA\Property(property: 'isSigned', type: 'boolean'),
            new OA\Property(property: 'team', type: 'array', items: new OA\Items(properties: [])),
            new OA\Property(property: 'type', type: 'string'),
        ];
    }
}

if (! function_exists('userDetailPropertiesDoc')) {
    function userDetailPropertiesDoc(): array
    {
        return [
            ...userCardPropertiesDoc(),
            new OA\Property(property: 'bio', type: 'string'),
            new OA\Property(property: 'filled', type: 'boolean'),
            new OA\Property(property: 'city', type: 'object', properties: [
                new OA\Property(property: 'id', type: 'string'),
                new OA\Property(property: 'title', type: 'string'),
            ]),
            new OA\Property(property: 'region', type: 'object', properties: [
                new OA\Property(property: 'id', type: 'string'),
                new OA\Property(property: 'title', type: 'string'),
            ]),
            new OA\Property(property: 'country', type: 'object', properties: [
                new OA\Property(property: 'id', type: 'string'),
                new OA\Property(property: 'title', type: 'string'),
            ]),
            new OA\Property(property: 'socials', type: 'array', items: new OA\Items(properties: [
                new OA\Property(property: 'id', type: 'string'),
                new OA\Property(property: 'title', type: 'string'),
                new OA\Property(property: 'url', type: 'string'),
            ])),
            new OA\Property(property: 'careers', type: 'array', items: new OA\Items(properties: [
                ...userCareerPropertiesDoc(),
            ])),
            new OA\Property(property: 'qualities', type: 'array', items: new OA\Items(properties: [
                new OA\Property(property: 'id', type: 'string'),
                new OA\Property(property: 'title', type: 'string'),
                new OA\Property(property: 'status', type: 'string'),
            ])),
            new OA\Property(property: 'rolesInProject', type: 'object', properties: [
                new OA\Property(property: 'mentor', type: 'boolean'),
                new OA\Property(property: 'investor', type: 'boolean'),
                new OA\Property(property: 'trainee', type: 'boolean'),
                new OA\Property(property: 'seeker', type: 'boolean'),
                new OA\Property(property: 'founder', type: 'boolean'),
            ]),
            new OA\Property(property: 'blog', type: 'array', items: new OA\Items(properties: [
                new OA\Property(property: 'id', type: 'string'),
                new OA\Property(property: 'title', type: 'string'),
                new OA\Property(property: 'slug', type: 'string'),
            ])),

        ];
    }
}

if (! function_exists('userNotifySettingsDoc')) {
    function userNotifySettingsDoc(): array
    {
        return [
            new OA\Property(property: 'showCommentsAnswer', type: 'boolean'),
            new OA\Property(property: 'showComments', type: 'boolean'),
            new OA\Property(property: 'showLikes', type: 'boolean'),
            new OA\Property(property: 'showPublicProjects', type: 'boolean'),
            new OA\Property(property: 'showRejectProjects', type: 'boolean'),
            new OA\Property(property: 'showBookmarks', type: 'boolean'),
            new OA\Property(property: 'showReports', type: 'boolean'),
        ];
    }
}

if (! function_exists('userEmailNotifySettingsDoc')) {
    function userEmailNotifySettingsDoc(): array
    {
        return [
            new OA\Property(property: 'commentAnswer', type: 'boolean'),
            new OA\Property(property: 'likeProject', type: 'boolean'),
            new OA\Property(property: 'popularProjects', type: 'boolean'),
            new OA\Property(property: 'newMessage', type: 'boolean'),
        ];
    }
}

if (! function_exists('userCareerPropertiesDoc')) {
    function userCareerPropertiesDoc(): array
    {
        return [
            new OA\Property(property: 'id', type: 'string'),
            new OA\Property(property: 'company', type: 'string'),
            new OA\Property(property: 'position', type: 'string'),
            new OA\Property(property: 'duty', type: 'string'),
            new OA\Property(property: 'start_date_at', type: 'string'),
            new OA\Property(property: 'last_date_at', type: 'string'),
            new OA\Property(property: 'created_at', type: 'string'),
        ];
    }
}

if (! function_exists('wikiSectionsPropertiesDoc')) {
    function wikiSectionsPropertiesDoc(): array
    {
        return [
            new OA\Property(property: 'id', type: 'string'),
            new OA\Property(property: 'title', type: 'string'),
            new OA\Property(property: 'parent_id', type: 'string'),
            new OA\Property(property: 'createdAt', type: 'string'),
        ];
    }
}

if (! function_exists('wikiArticlesPropertiesDoc')) {
    function wikiArticlesPropertiesDoc(): array
    {
        return [
            new OA\Property(property: 'id', type: 'string'),
            new OA\Property(property: 'title', type: 'string'),
            new OA\Property(property: 'text', type: 'string'),
            new OA\Property(property: 'section_id', type: 'string'),
            new OA\Property(property: 'hasDefault', type: 'boolean'),
            new OA\Property(property: 'createdAt', type: 'string'),
        ];
    }
}

if (! function_exists('notificationDataPropertyDoc')) {
    function notificationDataPropertyDoc(): OA\Property
    {
        return new OA\Property(property: 'data', type: 'object',
            properties: [
                new OA\Property(property: 'type', type: 'string'),
                new OA\Property(property: 'dataId,', type: 'string'),
                new OA\Property(property: 'title', type: 'string'),
                new OA\Property(property: 'dataSlug', type: 'string'),
                new OA\Property(property: 'author', type: 'object', properties: [
                    ...projectAuthorPropertiesDoc(),
                ]),
                new OA\Property(property: 'createdAt', type: 'string'),
            ]
        );
    }
}
