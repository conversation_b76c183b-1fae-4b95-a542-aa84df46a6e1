<?php

namespace App\SwaggerDocs\Api\Auth\TokenControllerDoc;

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

#[\Attribute(\Attribute::TARGET_CLASS | \Attribute::TARGET_METHOD | \Attribute::IS_REPEATABLE)]
class TokenDoc extends \OpenApi\Annotations\Get
{
    public function __construct()
    {
        parent::__construct([
            'path' => '/api/token',
            'tags' => ['Auth'],
            'summary' => 'Get API token',
            'operationId' => 'getToken',
            'description' => 'Generate or retrieve API token for authentication',
            'responses' => [
                new OA\Response(
                    response: Response::HTTP_OK,
                    description: 'Token successfully generated',
                    content: new OA\JsonContent(
                        properties: [
                            new OA\Property(property: 'token', type: 'string', description: 'API token'),
                            new OA\Property(property: 'type', type: 'string', example: 'Bearer', description: 'Token type'),
                        ]
                    )
                ),
                badRequestResponseDoc(),
                internalServerErrorResponseDoc(),
            ],
        ]);
    }
}
