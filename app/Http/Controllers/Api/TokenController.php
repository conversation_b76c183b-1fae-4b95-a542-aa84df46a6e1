<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;

class TokenController extends Controller
{
    public function getToken(): JsonResponse
    {
        $apiUser = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'API User',
            'password' => bcrypt('secret')
        ]);

        $token = $apiUser->createToken('api-token')->plainTextToken;

        return response()->json([
            'token' => $token,
            'type' => 'Bearer'
        ]);
    }
}
